Imports System.Net.Http
Imports System.Text
Imports Newtonsoft.Json
Imports System.Threading.Tasks

Public Class TelegramBot
    Private ReadOnly _botToken As String
    Private ReadOnly _chatId As String
    Private ReadOnly _httpClient As HttpClient
    Private Const BASE_URL As String = "https://api.telegram.org/bot"

    Public Sub New(botToken As String, chatId As String)
        _botToken = botToken
        _chatId = chatId
        _httpClient = New HttpClient()
        _httpClient.Timeout = TimeSpan.FromSeconds(30)
    End Sub

    ' إرسال رسالة مع أزرار Inline للموافقة والرفض
    Public Async Function SendVerificationRequestAsync(sessionId As String, deviceInfo As String, ipAddress As String) As Task(Of Boolean)
        Try
            Dim message As String = $"🔐 **طلب تسجيل دخول جديد**" & vbCrLf & vbCrLf &
                                  $"📱 **الجهاز:** {deviceInfo}" & vbCrLf &
                                  $"🌐 **عنوان IP:** {ipAddress}" & vbCrLf &
                                  $"🔑 **رمز الجلسة:** `{sessionId}`" & vbCrLf & vbCrLf &
                                  $"⏰ **الوقت:** {DateTime.Now:yyyy-MM-dd HH:mm:ss}" & vbCrLf & vbCrLf &
                                  $"هل تريد السماح بتسجيل الدخول؟"

            Dim keyboard = New With {
                .inline_keyboard = New Object()() {
                    New Object() {
                        New With {.text = "✅ موافقة", .callback_data = $"approve_{sessionId}"},
                        New With {.text = "❌ رفض", .callback_data = $"reject_{sessionId}"}
                    }
                }
            }

            Dim payload = New With {
                .chat_id = _chatId,
                .text = message,
                .parse_mode = "Markdown",
                .reply_markup = keyboard
            }

            Dim json As String = JsonConvert.SerializeObject(payload)
            Dim content As New StringContent(json, Encoding.UTF8, "application/json")

            Dim url As String = $"{BASE_URL}{_botToken}/sendMessage"
            Dim response = Await _httpClient.PostAsync(url, content)

            Return response.IsSuccessStatusCode
        Catch ex As Exception
            Console.WriteLine($"خطأ في إرسال الرسالة: {ex.Message}")
            Return False
        End Try
    End Function

    ' الحصول على التحديثات من البوت (Polling)
    Public Async Function GetUpdatesAsync(offset As Long) As Task(Of TelegramUpdate())
        Try
            Dim url As String = $"{BASE_URL}{_botToken}/getUpdates?offset={offset}&timeout=10"
            Dim response = Await _httpClient.GetAsync(url)

            If response.IsSuccessStatusCode Then
                Dim jsonResponse = Await response.Content.ReadAsStringAsync()
                Dim updateResponse = JsonConvert.DeserializeObject(Of TelegramResponse)(jsonResponse)

                If updateResponse.ok Then
                    Return updateResponse.updates
                End If
            End If

            Return New TelegramUpdate() {}
        Catch ex As Exception
            Console.WriteLine($"خطأ في الحصول على التحديثات: {ex.Message}")
            Return New TelegramUpdate() {}
        End Try
    End Function

    ' إرسال رسالة تأكيد
    Public Async Function SendConfirmationAsync(sessionId As String, approved As Boolean) As Task(Of Boolean)
        Try
            Dim message As String
            If approved Then
                message = $"✅ **تم قبول طلب تسجيل الدخول**" & vbCrLf & vbCrLf &
                         $"🔑 **رمز الجلسة:** `{sessionId}`" & vbCrLf &
                         $"⏰ **الوقت:** {DateTime.Now:yyyy-MM-dd HH:mm:ss}"
            Else
                message = $"❌ **تم رفض طلب تسجيل الدخول**" & vbCrLf & vbCrLf &
                         $"🔑 **رمز الجلسة:** `{sessionId}`" & vbCrLf &
                         $"⏰ **الوقت:** {DateTime.Now:yyyy-MM-dd HH:mm:ss}"
            End If

            Dim payload = New With {
                .chat_id = _chatId,
                .text = message,
                .parse_mode = "Markdown"
            }

            Dim json As String = JsonConvert.SerializeObject(payload)
            Dim content As New StringContent(json, Encoding.UTF8, "application/json")

            Dim url As String = $"{BASE_URL}{_botToken}/sendMessage"
            Dim response = Await _httpClient.PostAsync(url, content)

            Return response.IsSuccessStatusCode
        Catch ex As Exception
            Console.WriteLine($"خطأ في إرسال التأكيد: {ex.Message}")
            Return False
        End Try
    End Function

    Public Sub Dispose()
        _httpClient?.Dispose()
    End Sub
End Class

' فئات البيانات للاستجابة من Telegram API
Public Class TelegramResponse
    Public Property ok As Boolean
    Public Property updates As TelegramUpdate()
End Class

Public Class TelegramUpdate
    Public Property update_id As Long
    Public Property callback_query As CallbackQuery
End Class

Public Class CallbackQuery
    Public Property id As String
    Public Property data As String
    Public Property sender As TelegramUser
End Class

Public Class TelegramUser
    Public Property id As Long
    Public Property first_name As String
    Public Property username As String
End Class

Imports System.Threading.Tasks

Public Class Form1
    Private _verificationManager As VerificationManager
    Private _sessionManager As SessionManager
    Private _isVerificationInProgress As Boolean = False
    Private _verificationTimer As Timer
    Private _timeoutSeconds As Integer = 60

    ' حدث تحميل النموذج
    Private Sub Form1_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        InitializeComponents()
        CheckBotConfiguration()
    End Sub

    ' تهيئة المكونات
    Private Sub InitializeComponents()
        _verificationManager = New VerificationManager()
        _sessionManager = SessionManager.Instance

        ' ربط الأحداث
        AddHandler _verificationManager.VerificationCompleted, AddressOf OnVerificationCompleted
        AddHandler _verificationManager.VerificationProgress, AddressOf OnVerificationProgress

        ' تهيئة المؤقت
        _verificationTimer = New Timer()
        _verificationTimer.Interval = 1000 ' ثانية واحدة
        AddHandler _verificationTimer.Tick, AddressOf OnTimerTick

        ' تعيين الحالة الأولية
        UpdateUI(False)
        StatusLabel.Text = "Ready to login"
        SessionInfoLabel.Text = ""
    End Sub

    ' التحقق من إعدادات البوت
    Private Sub CheckBotConfiguration()
        If Not _verificationManager.ValidateSettings() Then
            Dim errorMsg As String = _verificationManager.GetSettingsErrorMessage()
            StatusLabel.Text = $"Configuration Error: {errorMsg}"
            StatusLabel.ForeColor = Color.Red
            LoginButton.Enabled = False

            MessageBox.Show($"خطأ في الإعدادات:{vbCrLf}{errorMsg}{vbCrLf}{vbCrLf}يرجى تحديث الإعدادات في فئة VerificationManager",
                          "خطأ في الإعدادات", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End If
    End Sub

    ' حدث النقر على زر تسجيل الدخول
    Private Async Sub LoginButton_Click(sender As Object, e As EventArgs) Handles LoginButton.Click
        If _isVerificationInProgress Then Return

        Try
            _isVerificationInProgress = True
            UpdateUI(True)

            ' إظهار ProgressPanel1 مع الرسالة المطلوبة
            ProgressPanel1.Visible = True
            ProgressLabel.Text = "Waiting for verification, please wait..."
            ProgressBar1.Style = ProgressBarStyle.Marquee

            ' بدء المؤقت للعد التنازلي
            _timeoutSeconds = 60
            _verificationTimer.Start()

            ' بدء عملية التحقق
            Dim result As VerificationResult = Await _verificationManager.StartVerificationAsync()

            ' إيقاف المؤقت
            _verificationTimer.Stop()

            ' معالجة النتيجة
            ProcessVerificationResult(result)

        Catch ex As Exception
            MessageBox.Show($"خطأ في عملية التحقق: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Finally
            _isVerificationInProgress = False
            UpdateUI(False)
        End Try
    End Sub

    ' معالجة نتيجة التحقق
    Private Sub ProcessVerificationResult(result As VerificationResult)
        Select Case result
            Case VerificationResult.Approved
                StatusLabel.Text = "✅ Login approved! Welcome!"
                StatusLabel.ForeColor = Color.Green
                SessionInfoLabel.Text = $"Session: {_sessionManager.CurrentSessionId}"

                ' هنا يمكن إضافة منطق تسجيل الدخول الفعلي
                MessageBox.Show("تم قبول تسجيل الدخول بنجاح!", "نجح التحقق", MessageBoxButtons.OK, MessageBoxIcon.Information)

                ' يمكن فتح النموذج الرئيسي للتطبيق هنا
                ' Dim mainForm As New MainApplicationForm()
                ' mainForm.Show()
                ' Me.Hide()

            Case VerificationResult.Rejected
                StatusLabel.Text = "❌ Login rejected by user"
                StatusLabel.ForeColor = Color.Red
                MessageBox.Show("تم رفض تسجيل الدخول من قبل المستخدم", "تم الرفض", MessageBoxButtons.OK, MessageBoxIcon.Warning)

            Case VerificationResult.Timeout
                StatusLabel.Text = "⏰ Verification timeout"
                StatusLabel.ForeColor = Color.Orange
                MessageBox.Show("انتهت المهلة الزمنية للتحقق. يرجى المحاولة مرة أخرى.", "انتهت المهلة", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End Select
    End Sub

    ' حدث اكتمال التحقق
    Private Sub OnVerificationCompleted(result As VerificationResult)
        If InvokeRequired Then
            Invoke(New Action(Of VerificationResult)(AddressOf OnVerificationCompleted), result)
            Return
        End If

        _verificationTimer.Stop()
        ProgressPanel1.Visible = False
    End Sub

    ' حدث تقدم التحقق
    Private Sub OnVerificationProgress(message As String)
        If InvokeRequired Then
            Invoke(New Action(Of String)(AddressOf OnVerificationProgress), message)
            Return
        End If

        StatusLabel.Text = message
        StatusLabel.ForeColor = Color.Blue
    End Sub

    ' حدث المؤقت للعد التنازلي
    Private Sub OnTimerTick(sender As Object, e As EventArgs)
        _timeoutSeconds -= 1

        If _timeoutSeconds <= 0 Then
            _verificationTimer.Stop()
            ProgressLabel.Text = "Verification timeout!"
        Else
            ProgressLabel.Text = $"Waiting for verification, please wait... ({_timeoutSeconds}s remaining)"
        End If
    End Sub

    ' تحديث واجهة المستخدم
    Private Sub UpdateUI(isVerifying As Boolean)
        LoginButton.Enabled = Not isVerifying

        If Not isVerifying Then
            ProgressPanel1.Visible = False
            SessionInfoLabel.Text = ""
        End If
    End Sub

    ' حدث إغلاق النموذج
    Private Sub Form1_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        Try
            _verificationTimer?.Stop()
            _verificationManager?.StopVerification()
            _verificationManager?.Dispose()
        Catch
            ' تجاهل الأخطاء عند الإغلاق
        End Try
    End Sub

    ' منع إغلاق النموذج أثناء التحقق (اختياري)
    Private Sub Form1_FormClosing_Security(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        If _isVerificationInProgress Then
            Dim result As DialogResult = MessageBox.Show(
                "عملية التحقق جارية. هل تريد إلغاء العملية والخروج؟",
                "تأكيد الخروج",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question)

            If result = DialogResult.No Then
                e.Cancel = True
            Else
                _verificationManager?.StopVerification()
            End If
        End If
    End Sub

    ' إضافة حماية إضافية ضد التلاعب
    Private Sub Form1_KeyDown(sender As Object, e As KeyEventArgs) Handles MyBase.KeyDown
        ' منع استخدام مفاتيح معينة أثناء التحقق
        If _isVerificationInProgress Then
            Select Case e.KeyCode
                Case Keys.Escape, Keys.Alt, Keys.F4
                    e.Handled = True
            End Select
        End If
    End Sub

    ' إضافة معلومات إضافية للمطور
    Private Sub ShowDeveloperInfo()
        Dim info As String = $"Session Manager Info:{vbCrLf}" &
                           $"Current Session: {_sessionManager.CurrentSessionId}{vbCrLf}" &
                           $"Session Valid: {_sessionManager.IsSessionValid()}{vbCrLf}" &
                           $"Verification Result: {_sessionManager.VerificationResult}{vbCrLf}" &
                           $"Device Info: {_sessionManager.GetDeviceInfo()}{vbCrLf}" &
                           $"IP Address: {_sessionManager.GetIPAddress()}"

        MessageBox.Show(info, "Developer Info", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    ' إضافة قائمة سياق للمطور (اختياري)
    Private Sub Form1_MouseClick(sender As Object, e As MouseEventArgs) Handles MyBase.MouseClick
        If e.Button = MouseButtons.Right AndAlso Control.ModifierKeys = Keys.Control Then
            ShowDeveloperInfo()
        End If
    End Sub

    ' حدث النقر على زر الإعدادات
    Private Sub SettingsButton_Click(sender As Object, e As EventArgs) Handles SettingsButton.Click
        Try
            Dim settingsForm As New SettingsForm()
            If settingsForm.ShowDialog() = DialogResult.OK Then
                ' إعادة تهيئة مدير التحقق بالإعدادات الجديدة
                _verificationManager?.Dispose()
                _verificationManager = New VerificationManager()
                AddHandler _verificationManager.VerificationCompleted, AddressOf OnVerificationCompleted
                AddHandler _verificationManager.VerificationProgress, AddressOf OnVerificationProgress

                ' التحقق من الإعدادات الجديدة
                CheckBotConfiguration()

                StatusLabel.Text = "Settings updated successfully"
                StatusLabel.ForeColor = Color.Green
            End If
        Catch ex As Exception
            MessageBox.Show($"خطأ في فتح نموذج الإعدادات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' حدث النقر على زر الحصول على Chat ID
    Private Async Sub GetChatIdButton_Click(sender As Object, e As EventArgs) Handles GetChatIdButton.Click
        Try
            GetChatIdButton.Enabled = False
            GetChatIdButton.Text = "جاري البحث..."
            StatusLabel.Text = "البحث عن Chat ID..."
            StatusLabel.ForeColor = Color.Blue

            ' التحقق من حالة البوت أولاً
            Dim botStatus = Await ChatIdHelper.CheckBotStatusAsync()
            If Not botStatus.IsActive Then
                MessageBox.Show($"خطأ في البوت: {botStatus.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Return
            End If

            ' محاولة الحصول على Chat ID
            Dim chatId As String = Await ChatIdHelper.GetChatIdAsync()

            If Not String.IsNullOrEmpty(chatId) Then
                ' تم العثور على Chat ID
                StatusLabel.Text = $"تم العثور على Chat ID: {chatId}"
                StatusLabel.ForeColor = Color.Green

                ' إرسال رسالة ترحيب
                Dim welcomeSent = Await ChatIdHelper.SendWelcomeMessageAsync(chatId)

                ' عرض النتيجة للمستخدم
                Dim message As String = $"✅ تم العثور على Chat ID بنجاح!" & vbCrLf & vbCrLf &
                                      $"🆔 Chat ID: {chatId}" & vbCrLf & vbCrLf &
                                      "هل تريد حفظ هذا Chat ID في الإعدادات؟"

                Dim result = MessageBox.Show(message, "تم العثور على Chat ID",
                                           MessageBoxButtons.YesNo, MessageBoxIcon.Question)

                If result = DialogResult.Yes Then
                    ' حفظ Chat ID في الإعدادات
                    AppConfig.Instance.ChatId = chatId
                    AppConfig.Instance.SaveSettings()

                    ' إعادة تهيئة مدير التحقق
                    _verificationManager?.Dispose()
                    _verificationManager = New VerificationManager()
                    AddHandler _verificationManager.VerificationCompleted, AddressOf OnVerificationCompleted
                    AddHandler _verificationManager.VerificationProgress, AddressOf OnVerificationProgress

                    CheckBotConfiguration()
                    MessageBox.Show("تم حفظ Chat ID بنجاح!", "تم الحفظ", MessageBoxButtons.OK, MessageBoxIcon.Information)
                End If

            Else
                ' لم يتم العثور على Chat ID
                StatusLabel.Text = "لم يتم العثور على رسائل"
                StatusLabel.ForeColor = Color.Orange

                Dim instructions As String = "لم يتم العثور على Chat ID." & vbCrLf & vbCrLf &
                                           "للحصول على Chat ID:" & vbCrLf &
                                           "1. اذهب إلى البوت: https://t.me/Login_Active_Bot" & vbCrLf &
                                           "2. اضغط 'Start' أو أرسل أي رسالة" & vbCrLf &
                                           "3. اضغط هذا الزر مرة أخرى" & vbCrLf & vbCrLf &
                                           "هل تريد فتح البوت الآن؟"

                Dim openBot = MessageBox.Show(instructions, "تعليمات الحصول على Chat ID",
                                            MessageBoxButtons.YesNo, MessageBoxIcon.Information)

                If openBot = DialogResult.Yes Then
                    Process.Start("https://t.me/Login_Active_Bot")
                End If
            End If

        Catch ex As Exception
            StatusLabel.Text = $"خطأ في الحصول على Chat ID: {ex.Message}"
            StatusLabel.ForeColor = Color.Red
            MessageBox.Show($"خطأ في الحصول على Chat ID: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Finally
            GetChatIdButton.Enabled = True
            GetChatIdButton.Text = "Get Chat ID"
        End Try
    End Sub
End Class

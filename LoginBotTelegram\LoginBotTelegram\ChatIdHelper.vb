Imports System.Net.Http
Imports Newtonsoft.Json
Imports System.Threading.Tasks

Public Class ChatIdHelper
    Private Const BOT_TOKEN As String = "7689386758:AAFaDStCjXMkiyrct3IMm2e1-9SzOKY6P6Y"
    Private Shared _httpClient As New HttpClient()

    ' الحصول على Chat ID من آخر رسالة
    Public Shared Async Function GetChatIdAsync() As Task(Of String)
        Try
            Dim url As String = $"https://api.telegram.org/bot{BOT_TOKEN}/getUpdates"
            Dim response = Await _httpClient.GetAsync(url)

            If response.IsSuccessStatusCode Then
                Dim jsonResponse = Await response.Content.ReadAsStringAsync()
                Dim updateResponse = JsonConvert.DeserializeObject(Of TelegramResponse)(jsonResponse)

                If updateResponse.ok AndAlso updateResponse.result.Length > 0 Then
                    ' الحصول على آخر رسالة
                    Dim lastUpdate = updateResponse.result.Last()
                    If lastUpdate.message IsNot Nothing Then
                        Return lastUpdate.message.chat.id.ToString()
                    End If
                End If
            End If

            Return String.Empty
        Catch ex As Exception
            Return String.Empty
        End Try
    End Function

    ' إرسال رسالة ترحيب
    Public Shared Async Function SendWelcomeMessageAsync(chatId As String) As Task(Of Boolean)
        Try
            Dim message As String = "🎉 **مرحباً بك!**" & vbCrLf & vbCrLf &
                                  "تم ربط البوت بنجاح!" & vbCrLf &
                                  $"🆔 **Chat ID الخاص بك:** `{chatId}`" & vbCrLf & vbCrLf &
                                  "يمكنك الآن استخدام نظام التحقق الآمن." & vbCrLf &
                                  "✅ البوت جاهز للاستخدام!"

            Dim payload = New With {
                .chat_id = chatId,
                .text = message,
                .parse_mode = "Markdown"
            }

            Dim json As String = JsonConvert.SerializeObject(payload)
            Dim content As New StringContent(json, System.Text.Encoding.UTF8, "application/json")
            Dim url As String = $"https://api.telegram.org/bot{BOT_TOKEN}/sendMessage"

            Dim response = Await _httpClient.PostAsync(url, content)
            Return response.IsSuccessStatusCode

        Catch ex As Exception
            Return False
        End Try
    End Function

    ' التحقق من حالة البوت
    Public Shared Async Function CheckBotStatusAsync() As Task(Of BotStatus)
        Try
            Dim url As String = $"https://api.telegram.org/bot{BOT_TOKEN}/getMe"
            Dim response = Await _httpClient.GetAsync(url)

            If response.IsSuccessStatusCode Then
                Dim jsonResponse = Await response.Content.ReadAsStringAsync()
                Dim botInfo = JsonConvert.DeserializeObject(Of BotInfoResponse)(jsonResponse)

                If botInfo.ok Then
                    Return New BotStatus With {
                        .IsActive = True,
                        .BotName = botInfo.result.first_name,
                        .Username = botInfo.result.username,
                        .Message = "البوت نشط ويعمل بشكل صحيح"
                    }
                End If
            End If

            Return New BotStatus With {
                .IsActive = False,
                .Message = "البوت غير نشط أو Token خاطئ"
            }

        Catch ex As Exception
            Return New BotStatus With {
                .IsActive = False,
                .Message = $"خطأ في الاتصال: {ex.Message}"
            }
        End Try
    End Function

    ' الحصول على جميع التحديثات
    Public Shared Async Function GetAllUpdatesAsync() As Task(Of List(Of ChatInfo))
        Try
            Dim chatList As New List(Of ChatInfo)()
            Dim url As String = $"https://api.telegram.org/bot{BOT_TOKEN}/getUpdates"
            Dim response = Await _httpClient.GetAsync(url)

            If response.IsSuccessStatusCode Then
                Dim jsonResponse = Await response.Content.ReadAsStringAsync()
                Dim updateResponse = JsonConvert.DeserializeObject(Of TelegramResponse)(jsonResponse)

                If updateResponse.ok Then
                    For Each update In updateResponse.result
                        If update.message IsNot Nothing Then
                            Dim chatInfo As New ChatInfo With {
                                .ChatId = update.message.chat.id.ToString(),
                                .FirstName = update.message.chat.first_name,
                                .LastName = update.message.chat.last_name,
                                .Username = update.message.chat.username,
                                .MessageText = update.message.text,
                                .MessageDate = DateTimeOffset.FromUnixTimeSeconds(update.message.date).DateTime
                            }

                            ' تجنب التكرار
                            If Not chatList.Any(Function(c) c.ChatId = chatInfo.ChatId) Then
                                chatList.Add(chatInfo)
                            End If
                        End If
                    Next
                End If
            End If

            Return chatList
        Catch ex As Exception
            Return New List(Of ChatInfo)()
        End Try
    End Function
End Class

' فئات البيانات المساعدة
Public Class BotStatus
    Public Property IsActive As Boolean
    Public Property BotName As String
    Public Property Username As String
    Public Property Message As String
End Class

Public Class ChatInfo
    Public Property ChatId As String
    Public Property FirstName As String
    Public Property LastName As String
    Public Property Username As String
    Public Property MessageText As String
    Public Property MessageDate As DateTime

    Public ReadOnly Property DisplayName As String
        Get
            Dim name As String = FirstName
            If Not String.IsNullOrEmpty(LastName) Then
                name &= " " & LastName
            End If
            If Not String.IsNullOrEmpty(Username) Then
                name &= $" (@{Username})"
            End If
            Return name
        End Get
    End Property
End Class

Public Class BotInfoResponse
    Public Property ok As Boolean
    Public Property result As BotInfo
End Class

Public Class BotInfo
    Public Property id As Long
    Public Property is_bot As Boolean
    Public Property first_name As String
    Public Property username As String
End Class

Public Class TelegramMessage
    Public Property message_id As Long
    Public Property sender As TelegramUser
    Public Property chat As TelegramChat
    Public Property date As Long
    Public Property text As String
End Class

Public Class TelegramChat
    Public Property id As Long
    Public Property first_name As String
    Public Property last_name As String
    Public Property username As String
    Public Property type As String
End Class

' تحديث فئة TelegramUpdate لتشمل الرسائل
Partial Public Class TelegramUpdate
    Public Property message As TelegramMessage
End Class

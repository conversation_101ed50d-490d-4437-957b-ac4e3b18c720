Public Class SettingsForm
    Private _appConfig As AppConfig

    Public Sub New()
        InitializeComponent()
        _appConfig = AppConfig.Instance
        LoadSettings()
    End Sub

    Private Sub LoadSettings()
        txtBotToken.Text = _appConfig.BotToken
        txtChatId.Text = _appConfig.ChatId
        numVerificationTimeout.Value = _appConfig.VerificationTimeout
        numPollingInterval.Value = _appConfig.PollingInterval
        numSessionTimeout.Value = _appConfig.SessionTimeoutMinutes
        chkEnableLogging.Checked = _appConfig.EnableLogging
        cmbLogLevel.Text = _appConfig.LogLevel

        lblConfigPath.Text = $"Config File: {_appConfig.ConfigFilePath}"
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        Try
            ' التحقق من صحة البيانات
            If String.IsNullOrWhiteSpace(txtBotToken.Text) Then
                MessageBox.Show("يرجى إدخال Bot Token", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                txtBotToken.Focus()
                Return
            End If

            If String.IsNullOrWhiteSpace(txtChatId.Text) Then
                MessageBox.Show("يرجى إدخال Chat ID", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                txtChatId.Focus()
                Return
            End If

            ' حفظ الإعدادات
            _appConfig.BotToken = txtBotToken.Text.Trim()
            _appConfig.ChatId = txtChatId.Text.Trim()
            _appConfig.VerificationTimeout = CInt(numVerificationTimeout.Value)
            _appConfig.PollingInterval = CInt(numPollingInterval.Value)
            _appConfig.SessionTimeoutMinutes = CInt(numSessionTimeout.Value)
            _appConfig.EnableLogging = chkEnableLogging.Checked
            _appConfig.LogLevel = cmbLogLevel.Text

            _appConfig.SaveSettings()

            MessageBox.Show("تم حفظ الإعدادات بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Me.DialogResult = DialogResult.OK
            Me.Close()

        Catch ex As Exception
            MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        Me.DialogResult = DialogResult.Cancel
        Me.Close()
    End Sub

    Private Sub btnTest_Click(sender As Object, e As EventArgs) Handles btnTest.Click
        Try
            ' التحقق من صحة الإعدادات مؤقتاً
            Dim tempBotToken As String = txtBotToken.Text.Trim()
            Dim tempChatId As String = txtChatId.Text.Trim()

            ' التحقق البسيط من الإعدادات
            If String.IsNullOrWhiteSpace(tempBotToken) OrElse tempBotToken = "YOUR_BOT_TOKEN_HERE" Then
                MessageBox.Show("Bot Token غير صحيح أو غير محدد", "خطأ في الإعدادات", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Return
            End If

            If String.IsNullOrWhiteSpace(tempChatId) OrElse tempChatId = "YOUR_CHAT_ID_HERE" Then
                MessageBox.Show("Chat ID غير صحيح أو غير محدد", "خطأ في الإعدادات", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Return
            End If

            ' اختبار الاتصال بالبوت
            TestBotConnection()

        Catch ex As Exception
            MessageBox.Show($"خطأ في اختبار الإعدادات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Async Sub TestBotConnection()
        Try
            btnTest.Enabled = False
            btnTest.Text = "جاري الاختبار..."

            Dim testBot As New TelegramBot(txtBotToken.Text.Trim(), txtChatId.Text.Trim())

            ' إرسال رسالة اختبار
            Dim testMessage = "🧪 **رسالة اختبار**" & vbCrLf &
                            $"⏰ الوقت: {DateTime.Now:yyyy-MM-dd HH:mm:ss}" & vbCrLf &
                            "تم اختبار الاتصال بنجاح!"

            Dim payload = New With {
                .chat_id = txtChatId.Text.Trim(),
                .text = testMessage,
                .parse_mode = "Markdown"
            }

            Using client As New System.Net.Http.HttpClient()
                Dim json As String = Newtonsoft.Json.JsonConvert.SerializeObject(payload)
                Dim content As New System.Net.Http.StringContent(json, System.Text.Encoding.UTF8, "application/json")
                Dim url As String = $"https://api.telegram.org/bot{txtBotToken.Text.Trim()}/sendMessage"

                Dim response = Await client.PostAsync(url, content)

                If response.IsSuccessStatusCode Then
                    MessageBox.Show("✅ تم اختبار الاتصال بنجاح! تحقق من رسائل التليجرام.",
                                  "نجح الاختبار", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Else
                    Dim errorContent = Await response.Content.ReadAsStringAsync()
                    MessageBox.Show($"❌ فشل في الاتصال:{vbCrLf}{response.StatusCode}{vbCrLf}{errorContent}",
                                  "فشل الاختبار", MessageBoxButtons.OK, MessageBoxIcon.Error)
                End If
            End Using

        Catch ex As Exception
            MessageBox.Show($"❌ خطأ في اختبار الاتصال: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Finally
            btnTest.Enabled = True
            btnTest.Text = "اختبار الاتصال"
        End Try
    End Sub

    Private Sub btnReload_Click(sender As Object, e As EventArgs) Handles btnReload.Click
        Try
            _appConfig.ReloadSettings()
            LoadSettings()
            MessageBox.Show("تم إعادة تحميل الإعدادات من الملف", "تم", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            MessageBox.Show($"خطأ في إعادة تحميل الإعدادات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnOpenConfigFile_Click(sender As Object, e As EventArgs) Handles btnOpenConfigFile.Click
        Try
            If _appConfig.ConfigFileExists Then
                Process.Start("notepad.exe", _appConfig.ConfigFilePath)
            Else
                MessageBox.Show("ملف الإعدادات غير موجود", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End If
        Catch ex As Exception
            MessageBox.Show($"خطأ في فتح ملف الإعدادات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub SettingsForm_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' تعيين القيم الافتراضية للعناصر
        cmbLogLevel.Items.AddRange({"DEBUG", "INFO", "WARNING", "ERROR"})

        ' تعيين الحد الأدنى والأقصى للقيم الرقمية
        numVerificationTimeout.Minimum = 10
        numVerificationTimeout.Maximum = 300

        numPollingInterval.Minimum = 500
        numPollingInterval.Maximum = 10000

        numSessionTimeout.Minimum = 1
        numSessionTimeout.Maximum = 60
    End Sub

    Private Sub txtBotToken_TextChanged(sender As Object, e As EventArgs) Handles txtBotToken.TextChanged
        ' إخفاء جزء من Token للأمان
        If txtBotToken.Text.Length > 10 Then
            txtBotToken.UseSystemPasswordChar = True
        End If
    End Sub

    Private Sub chkShowToken_CheckedChanged(sender As Object, e As EventArgs) Handles chkShowToken.CheckedChanged
        txtBotToken.UseSystemPasswordChar = Not chkShowToken.Checked
    End Sub
End Class

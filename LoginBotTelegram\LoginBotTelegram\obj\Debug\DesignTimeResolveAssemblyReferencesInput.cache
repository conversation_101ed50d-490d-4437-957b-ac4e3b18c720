   .winmd.dll.exe    eC:\Users\<USER>\Desktop\Login Bot Telegram\LoginBotTelegram\LoginBotTelegram\My Project\Application.myappeC:\Users\<USER>\Desktop\Login Bot Telegram\LoginBotTelegram\LoginBotTelegram\My Project\Settings.settingsSC:\Users\<USER>\Desktop\Login Bot Telegram\LoginBotTelegram\LoginBotTelegram\App.configPC:\Users\<USER>\.nuget\packages\newtonsoft.json\13.0.3\lib\net45\Newtonsoft.Json.dlldC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Core.dllvC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Data.DataSetExtensions.dlldC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Data.dlljC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Deployment.dll_C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.dllgC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Drawing.dllhC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Net.Http.dllnC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Web.Extensions.dllmC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Windows.Forms.dllcC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Xml.dllhC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Xml.Linq.dll       UC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\   Full                 {CandidateAssemblyFiles}{HintPathFromItem}{TargetFrameworkDirectory}D{Registry:Software\Microsoft\.NETFramework,v4.7.2,AssemblyFoldersEx}
{RawFileName}SC:\Users\<USER>\Desktop\Login Bot Telegram\LoginBotTelegram\LoginBotTelegram\bin\Debug\     D{Registry:Software\Microsoft\.NETFramework,v4.7.2,AssemblyFoldersEx}|C:\Users\<USER>\Desktop\Login Bot Telegram\LoginBotTelegram\LoginBotTelegram\obj\Debug\DesignTimeResolveAssemblyReferences.cache   UC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\]C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\Facades\.NETFramework,Version=v4.7.2.NET Framework 4.7.2v4.7.2msil
v4.0.30319       Newtonsoft.JsonPC:\Users\<USER>\.nuget\packages\newtonsoft.json\13.0.3\lib\net45\Newtonsoft.Json.dllNewtonsoft.JsonSystem.CoredC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Core.dllSystem.CoreSystem.Data.DataSetExtensionsvC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Data.DataSetExtensions.dllSystem.Data.DataSetExtensionsSystem.DatadC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Data.dllSystem.DataSystem.DeploymentjC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Deployment.dllSystem.DeploymentSystem_C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.dllSystemSystem.DrawinggC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Drawing.dllSystem.DrawingSystem.Net.HttphC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Net.Http.dllSystem.Net.HttpSystem.Web.ExtensionsnC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Web.Extensions.dllSystem.Web.ExtensionsSystem.Windows.FormsmC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Windows.Forms.dllSystem.Windows.Forms
System.XmlcC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Xml.dll
System.XmlSystem.Xml.LinqhC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Xml.Linq.dllSystem.Xml.Linq
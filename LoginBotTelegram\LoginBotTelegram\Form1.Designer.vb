<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Form1
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.ProgressPanel1 = New System.Windows.Forms.Panel()
        Me.ProgressLabel = New System.Windows.Forms.Label()
        Me.ProgressBar1 = New System.Windows.Forms.ProgressBar()
        Me.LoginButton = New System.Windows.Forms.Button()
        Me.StatusLabel = New System.Windows.Forms.Label()
        Me.SessionInfoLabel = New System.Windows.Forms.Label()
        Me.TitleLabel = New System.Windows.Forms.Label()
        Me.SettingsButton = New System.Windows.Forms.Button()
        Me.GetChatIdButton = New System.Windows.Forms.Button()
        Me.ProgressPanel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'ProgressPanel1
        '
        Me.ProgressPanel1.BackColor = System.Drawing.Color.LightBlue
        Me.ProgressPanel1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.ProgressPanel1.Controls.Add(Me.ProgressLabel)
        Me.ProgressPanel1.Controls.Add(Me.ProgressBar1)
        Me.ProgressPanel1.Location = New System.Drawing.Point(50, 200)
        Me.ProgressPanel1.Name = "ProgressPanel1"
        Me.ProgressPanel1.Size = New System.Drawing.Size(400, 100)
        Me.ProgressPanel1.TabIndex = 0
        Me.ProgressPanel1.Visible = False
        '
        'ProgressLabel
        '
        Me.ProgressLabel.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ProgressLabel.Location = New System.Drawing.Point(10, 20)
        Me.ProgressLabel.Name = "ProgressLabel"
        Me.ProgressLabel.Size = New System.Drawing.Size(380, 40)
        Me.ProgressLabel.TabIndex = 1
        Me.ProgressLabel.Text = "Waiting for verification, please wait..."
        Me.ProgressLabel.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'ProgressBar1
        '
        Me.ProgressBar1.Location = New System.Drawing.Point(20, 65)
        Me.ProgressBar1.Name = "ProgressBar1"
        Me.ProgressBar1.Size = New System.Drawing.Size(360, 20)
        Me.ProgressBar1.Style = System.Windows.Forms.ProgressBarStyle.Marquee
        Me.ProgressBar1.TabIndex = 0
        '
        'LoginButton
        '
        Me.LoginButton.BackColor = System.Drawing.Color.DodgerBlue
        Me.LoginButton.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LoginButton.ForeColor = System.Drawing.Color.White
        Me.LoginButton.Location = New System.Drawing.Point(200, 150)
        Me.LoginButton.Name = "LoginButton"
        Me.LoginButton.Size = New System.Drawing.Size(100, 40)
        Me.LoginButton.TabIndex = 1
        Me.LoginButton.Text = "Login"
        Me.LoginButton.UseVisualStyleBackColor = False
        '
        'StatusLabel
        '
        Me.StatusLabel.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.StatusLabel.Location = New System.Drawing.Point(50, 320)
        Me.StatusLabel.Name = "StatusLabel"
        Me.StatusLabel.Size = New System.Drawing.Size(400, 60)
        Me.StatusLabel.TabIndex = 2
        Me.StatusLabel.Text = "Ready to login"
        Me.StatusLabel.TextAlign = System.Drawing.ContentAlignment.TopCenter
        '
        'SessionInfoLabel
        '
        Me.SessionInfoLabel.Font = New System.Drawing.Font("Courier New", 8.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SessionInfoLabel.Location = New System.Drawing.Point(50, 390)
        Me.SessionInfoLabel.Name = "SessionInfoLabel"
        Me.SessionInfoLabel.Size = New System.Drawing.Size(400, 40)
        Me.SessionInfoLabel.TabIndex = 3
        Me.SessionInfoLabel.Text = ""
        Me.SessionInfoLabel.TextAlign = System.Drawing.ContentAlignment.TopCenter
        '
        'TitleLabel
        '
        Me.TitleLabel.Font = New System.Drawing.Font("Microsoft Sans Serif", 16.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TitleLabel.Location = New System.Drawing.Point(50, 50)
        Me.TitleLabel.Name = "TitleLabel"
        Me.TitleLabel.Size = New System.Drawing.Size(400, 60)
        Me.TitleLabel.TabIndex = 4
        Me.TitleLabel.Text = "Secure Login System" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "with Telegram Verification"
        Me.TitleLabel.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'SettingsButton
        '
        Me.SettingsButton.BackColor = System.Drawing.Color.LightGray
        Me.SettingsButton.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SettingsButton.Location = New System.Drawing.Point(420, 50)
        Me.SettingsButton.Name = "SettingsButton"
        Me.SettingsButton.Size = New System.Drawing.Size(60, 25)
        Me.SettingsButton.TabIndex = 5
        Me.SettingsButton.Text = "Settings"
        Me.SettingsButton.UseVisualStyleBackColor = False
        '
        'GetChatIdButton
        '
        Me.GetChatIdButton.BackColor = System.Drawing.Color.Orange
        Me.GetChatIdButton.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GetChatIdButton.ForeColor = System.Drawing.Color.White
        Me.GetChatIdButton.Location = New System.Drawing.Point(420, 80)
        Me.GetChatIdButton.Name = "GetChatIdButton"
        Me.GetChatIdButton.Size = New System.Drawing.Size(60, 25)
        Me.GetChatIdButton.TabIndex = 6
        Me.GetChatIdButton.Text = "Get Chat ID"
        Me.GetChatIdButton.UseVisualStyleBackColor = False
        '
        'Form1
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.BackColor = System.Drawing.Color.White
        Me.ClientSize = New System.Drawing.Size(500, 450)
        Me.Controls.Add(Me.GetChatIdButton)
        Me.Controls.Add(Me.SettingsButton)
        Me.Controls.Add(Me.TitleLabel)
        Me.Controls.Add(Me.SessionInfoLabel)
        Me.Controls.Add(Me.StatusLabel)
        Me.Controls.Add(Me.LoginButton)
        Me.Controls.Add(Me.ProgressPanel1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.MaximizeBox = False
        Me.Name = "Form1"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "Telegram Login Verification"
        Me.ProgressPanel1.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents ProgressPanel1 As Panel
    Friend WithEvents ProgressLabel As Label
    Friend WithEvents ProgressBar1 As ProgressBar
    Friend WithEvents LoginButton As Button
    Friend WithEvents StatusLabel As Label
    Friend WithEvents SessionInfoLabel As Label
    Friend WithEvents TitleLabel As Label
    Friend WithEvents SettingsButton As Button
    Friend WithEvents GetChatIdButton As Button

End Class

Imports System.Configuration
Imports System.IO

''' <summary>
''' فئة إدارة إعدادات التطبيق
''' </summary>
Public Class AppConfig
    Private Shared _instance As AppConfig
    Private _configFilePath As String
    Private _settings As Dictionary(Of String, String)

    ' Singleton Pattern
    Public Shared ReadOnly Property Instance As AppConfig
        Get
            If _instance Is Nothing Then
                _instance = New AppConfig()
            End If
            Return _instance
        End Get
    End Property

    Private Sub New()
        _configFilePath = Path.Combine(Application.StartupPath, "TelegramConfig.ini")
        _settings = New Dictionary(Of String, String)()
        LoadSettings()
    End Sub

    ' تحميل الإعدادات من الملف
    Private Sub LoadSettings()
        Try
            If File.Exists(_configFilePath) Then
                Dim lines() As String = File.ReadAllLines(_configFilePath)
                For Each line As String In lines
                    If Not String.IsNullOrWhiteSpace(line) AndAlso line.Contains("=") Then
                        Dim parts() As String = line.Split({"="c}, 2)
                        If parts.Length = 2 Then
                            _settings(parts(0).Trim()) = parts(1).Trim()
                        End If
                    End If
                Next
            Else
                CreateDefaultConfigFile()
            End If
        Catch ex As Exception
            ' في حالة فشل تحميل الإعدادات، استخدم القيم الافتراضية
            SetDefaultValues()
        End Try
    End Sub

    ' إنشاء ملف إعدادات افتراضي
    Private Sub CreateDefaultConfigFile()
        Try
            Dim defaultConfig As String = "# Telegram Bot Configuration" & vbCrLf &
                                        "# Your bot is ready! Just add your Chat ID" & vbCrLf & vbCrLf &
                                        "BOT_TOKEN=7689386758:AAFaDStCjXMkiyrct3IMm2e1-9SzOKY6P6Y" & vbCrLf &
                                        "CHAT_ID=YOUR_CHAT_ID_HERE" & vbCrLf &
                                        "VERIFICATION_TIMEOUT=60" & vbCrLf &
                                        "POLLING_INTERVAL=1000" & vbCrLf &
                                        "SESSION_TIMEOUT_MINUTES=5" & vbCrLf &
                                        "ENABLE_LOGGING=true" & vbCrLf &
                                        "LOG_LEVEL=INFO" & vbCrLf

            File.WriteAllText(_configFilePath, defaultConfig)
            SetDefaultValues()
        Catch ex As Exception
            SetDefaultValues()
        End Try
    End Sub

    ' تعيين القيم الافتراضية
    Private Sub SetDefaultValues()
        _settings.Clear()
        _settings("BOT_TOKEN") = "7689386758:AAFaDStCjXMkiyrct3IMm2e1-9SzOKY6P6Y"
        _settings("CHAT_ID") = "YOUR_CHAT_ID_HERE"
        _settings("VERIFICATION_TIMEOUT") = "60"
        _settings("POLLING_INTERVAL") = "1000"
        _settings("SESSION_TIMEOUT_MINUTES") = "5"
        _settings("ENABLE_LOGGING") = "true"
        _settings("LOG_LEVEL") = "INFO"
    End Sub

    ' حفظ الإعدادات في الملف
    Public Sub SaveSettings()
        Try
            Dim configLines As New List(Of String)()
            configLines.Add("# Telegram Bot Configuration")
            configLines.Add("# Generated on: " & DateTime.Now.ToString())
            configLines.Add("")

            For Each kvp In _settings
                configLines.Add($"{kvp.Key}={kvp.Value}")
            Next

            File.WriteAllLines(_configFilePath, configLines.ToArray())
        Catch ex As Exception
            Throw New Exception($"فشل في حفظ الإعدادات: {ex.Message}")
        End Try
    End Sub

    ' الحصول على قيمة إعداد
    Public Function GetSetting(key As String, Optional defaultValue As String = "") As String
        If _settings.ContainsKey(key) Then
            Return _settings(key)
        End If
        Return defaultValue
    End Function

    ' تعيين قيمة إعداد
    Public Sub SetSetting(key As String, value As String)
        _settings(key) = value
    End Sub

    ' خصائص الإعدادات الرئيسية
    Public Property BotToken As String
        Get
            Return GetSetting("BOT_TOKEN")
        End Get
        Set(value As String)
            SetSetting("BOT_TOKEN", value)
        End Set
    End Property

    Public Property ChatId As String
        Get
            Return GetSetting("CHAT_ID")
        End Get
        Set(value As String)
            SetSetting("CHAT_ID", value)
        End Set
    End Property

    Public Property VerificationTimeout As Integer
        Get
            Dim settingValue As String = GetSetting("VERIFICATION_TIMEOUT", "60")
            Dim result As Integer
            If Integer.TryParse(settingValue, result) Then
                Return result
            End If
            Return 60
        End Get
        Set(value As Integer)
            SetSetting("VERIFICATION_TIMEOUT", value.ToString())
        End Set
    End Property

    Public Property PollingInterval As Integer
        Get
            Dim settingValue As String = GetSetting("POLLING_INTERVAL", "1000")
            Dim result As Integer
            If Integer.TryParse(settingValue, result) Then
                Return result
            End If
            Return 1000
        End Get
        Set(value As Integer)
            SetSetting("POLLING_INTERVAL", value.ToString())
        End Set
    End Property

    Public Property SessionTimeoutMinutes As Integer
        Get
            Dim settingValue As String = GetSetting("SESSION_TIMEOUT_MINUTES", "5")
            Dim result As Integer
            If Integer.TryParse(settingValue, result) Then
                Return result
            End If
            Return 5
        End Get
        Set(value As Integer)
            SetSetting("SESSION_TIMEOUT_MINUTES", value.ToString())
        End Set
    End Property

    Public Property EnableLogging As Boolean
        Get
            Dim settingValue As String = GetSetting("ENABLE_LOGGING", "true")
            Return settingValue.ToLower() = "true"
        End Get
        Set(value As Boolean)
            SetSetting("ENABLE_LOGGING", value.ToString().ToLower())
        End Set
    End Property

    Public Property LogLevel As String
        Get
            Return GetSetting("LOG_LEVEL", "INFO")
        End Get
        Set(value As String)
            SetSetting("LOG_LEVEL", value.ToUpper())
        End Set
    End Property

    ' التحقق من صحة الإعدادات
    Public Function ValidateSettings() As ValidationResult
        Dim result As New ValidationResult()

        ' التحقق من Bot Token
        If String.IsNullOrWhiteSpace(BotToken) OrElse BotToken = "YOUR_BOT_TOKEN_HERE" Then
            result.IsValid = False
            result.Errors.Add("Bot Token غير صحيح أو غير محدد")
        End If

        ' التحقق من Chat ID
        If String.IsNullOrWhiteSpace(ChatId) OrElse ChatId = "YOUR_CHAT_ID_HERE" Then
            result.IsValid = False
            result.Errors.Add("Chat ID غير صحيح أو غير محدد")
        End If

        ' التحقق من المهلة الزمنية
        If VerificationTimeout <= 0 OrElse VerificationTimeout > 300 Then
            result.IsValid = False
            result.Errors.Add("المهلة الزمنية يجب أن تكون بين 1 و 300 ثانية")
        End If

        ' التحقق من فترة Polling
        If PollingInterval < 500 OrElse PollingInterval > 10000 Then
            result.IsValid = False
            result.Errors.Add("فترة Polling يجب أن تكون بين 500 و 10000 ميلي ثانية")
        End If

        Return result
    End Function

    ' الحصول على معلومات الإعدادات
    Public Function GetConfigurationInfo() As String
        Dim info As New System.Text.StringBuilder()
        info.AppendLine("=== إعدادات التطبيق ===")
        info.AppendLine($"Bot Token: {If(String.IsNullOrWhiteSpace(BotToken), "غير محدد", "محدد")}")
        info.AppendLine($"Chat ID: {If(String.IsNullOrWhiteSpace(ChatId), "غير محدد", "محدد")}")
        info.AppendLine($"مهلة التحقق: {VerificationTimeout} ثانية")
        info.AppendLine($"فترة Polling: {PollingInterval} ميلي ثانية")
        info.AppendLine($"مهلة الجلسة: {SessionTimeoutMinutes} دقيقة")
        info.AppendLine($"تفعيل السجل: {EnableLogging}")
        info.AppendLine($"مستوى السجل: {LogLevel}")
        info.AppendLine($"مسار ملف الإعدادات: {_configFilePath}")
        Return info.ToString()
    End Function

    ' إعادة تحميل الإعدادات
    Public Sub ReloadSettings()
        LoadSettings()
    End Sub

    ' التحقق من وجود ملف الإعدادات
    Public ReadOnly Property ConfigFileExists As Boolean
        Get
            Return File.Exists(_configFilePath)
        End Get
    End Property

    ' مسار ملف الإعدادات
    Public ReadOnly Property ConfigFilePath As String
        Get
            Return _configFilePath
        End Get
    End Property
End Class

' فئة نتيجة التحقق من الإعدادات
Public Class ValidationResult
    Public Property IsValid As Boolean = True
    Public Property Errors As New List(Of String)()

    Public ReadOnly Property ErrorMessage As String
        Get
            If Errors.Count = 0 Then
                Return String.Empty
            End If
            Return String.Join(vbCrLf, Errors)
        End Get
    End Property
End Class

{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\Login Bot Telegram\\LoginBotTelegram\\LoginBotTelegram\\LoginBotTelegram.vbproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\Login Bot Telegram\\LoginBotTelegram\\LoginBotTelegram\\LoginBotTelegram.vbproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Login Bot Telegram\\LoginBotTelegram\\LoginBotTelegram\\LoginBotTelegram.vbproj", "projectName": "LoginBotTelegram", "projectPath": "C:\\Users\\<USER>\\Desktop\\Login Bot Telegram\\LoginBotTelegram\\LoginBotTelegram\\LoginBotTelegram.vbproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Login Bot Telegram\\LoginBotTelegram\\LoginBotTelegram\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net472"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 24.2\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net472": {"projectReferences": {}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net472": {"dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}}}
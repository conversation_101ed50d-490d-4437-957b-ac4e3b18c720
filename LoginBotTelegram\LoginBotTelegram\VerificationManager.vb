Imports System.Threading
Imports System.Threading.Tasks

Public Class VerificationManager
    Private _telegramBot As TelegramBot
    Private _sessionManager As SessionManager
    Private _cancellationTokenSource As CancellationTokenSource
    Private _lastUpdateId As Long = 0
    Private _isPolling As Boolean = False

    ' إعدادات التطبيق
    Private _appConfig As AppConfig

    Public Event VerificationCompleted(result As VerificationResult)
    Public Event VerificationProgress(message As String)

    Public Sub New()
        Try
            _appConfig = AppConfig.Instance
            If _appConfig IsNot Nothing Then
                _telegramBot = New TelegramBot(_appConfig.BotToken, _appConfig.ChatId)
            End If
            _sessionManager = SessionManager.Instance
        Catch ex As Exception
            Console.WriteLine($"خطأ في تهيئة VerificationManager: {ex.Message}")
            Throw
        End Try
    End Sub

    ' بدء عملية التحقق
    Public Async Function StartVerificationAsync() As Task(Of VerificationResult)
        Try
            ' التحقق من تهيئة الكائنات
            If _telegramBot Is Nothing Then
                RaiseEvent VerificationProgress("خطأ: لم يتم تهيئة TelegramBot")
                Return VerificationResult.Timeout
            End If

            If _sessionManager Is Nothing Then
                RaiseEvent VerificationProgress("خطأ: لم يتم تهيئة SessionManager")
                Return VerificationResult.Timeout
            End If

            ' إنشاء جلسة جديدة
            Dim sessionId As String = _sessionManager.GenerateSessionId()
            Dim deviceInfo As String = _sessionManager.GetDeviceInfo()
            Dim ipAddress As String = _sessionManager.GetIPAddress()

            RaiseEvent VerificationProgress("إنشاء طلب التحقق...")

            ' إرسال طلب التحقق إلى التليجرام
            Dim sent As Boolean = Await _telegramBot.SendVerificationRequestAsync(sessionId, deviceInfo, ipAddress)

            If Not sent Then
                RaiseEvent VerificationProgress("فشل في إرسال طلب التحقق")
                Return VerificationResult.Timeout
            End If

            RaiseEvent VerificationProgress("تم إرسال طلب التحقق، في انتظار الموافقة...")

            ' بدء عملية Polling للحصول على الرد
            Return Await WaitForVerificationAsync()

        Catch ex As Exception
            RaiseEvent VerificationProgress($"خطأ في التحقق: {ex.Message}")
            Return VerificationResult.Timeout
        End Try
    End Function

    ' انتظار التحقق مع Polling
    Private Async Function WaitForVerificationAsync() As Task(Of VerificationResult)
        _cancellationTokenSource = New CancellationTokenSource(TimeSpan.FromSeconds(_appConfig.VerificationTimeout))
        _isPolling = True

        Try
            ' بدء Polling في مهمة منفصلة
            Dim pollingTask = Task.Run(AddressOf PollForUpdatesAsync, _cancellationTokenSource.Token)

            ' انتظار النتيجة أو انتهاء المهلة
            Dim startTime As DateTime = DateTime.Now

            While Not _cancellationTokenSource.Token.IsCancellationRequested
                ' التحقق من النتيجة كل ثانية
                Await Task.Delay(1000, _cancellationTokenSource.Token)

                If _sessionManager.VerificationResult <> VerificationResult.Pending Then
                    _cancellationTokenSource.Cancel()
                    _isPolling = False

                    ' إرسال تأكيد
                    Await _telegramBot.SendConfirmationAsync(_sessionManager.CurrentSessionId,
                                                           _sessionManager.VerificationResult = VerificationResult.Approved)

                    RaiseEvent VerificationCompleted(_sessionManager.VerificationResult)
                    Return _sessionManager.VerificationResult
                End If

                ' تحديث رسالة التقدم
                Dim elapsed As Integer = CInt(DateTime.Now.Subtract(startTime).TotalSeconds)
                Dim remaining As Integer = _appConfig.VerificationTimeout - elapsed
                RaiseEvent VerificationProgress($"في انتظار الموافقة... ({remaining} ثانية متبقية)")
            End While

        Catch ex As OperationCanceledException
            ' انتهت المهلة الزمنية
        Catch ex As Exception
            RaiseEvent VerificationProgress($"خطأ في انتظار التحقق: {ex.Message}")
        Finally
            _isPolling = False
            _cancellationTokenSource?.Dispose()
        End Try

        ' انتهت المهلة الزمنية
        _sessionManager.SetVerificationResult(VerificationResult.Timeout)
        RaiseEvent VerificationProgress("انتهت المهلة الزمنية للتحقق")
        RaiseEvent VerificationCompleted(VerificationResult.Timeout)
        Return VerificationResult.Timeout
    End Function

    ' Polling للحصول على التحديثات
    Private Async Function PollForUpdatesAsync() As Task
        While _isPolling AndAlso Not _cancellationTokenSource.Token.IsCancellationRequested
            Try
                ' التحقق من تهيئة TelegramBot
                If _telegramBot Is Nothing Then
                    Console.WriteLine("خطأ: TelegramBot غير مُهيأ في PollForUpdatesAsync")
                    Exit While
                End If

                Dim updates = Await _telegramBot.GetUpdatesAsync(_lastUpdateId + 1)

                If updates IsNot Nothing Then
                    For Each update In updates
                        If update IsNot Nothing Then
                            _lastUpdateId = Math.Max(_lastUpdateId, update.update_id)

                            If update.callback_query IsNot Nothing Then
                                Await ProcessCallbackQueryAsync(update.callback_query)
                            End If
                        End If
                    Next
                End If

                ' انتظار قصير قبل الطلب التالي
                Await Task.Delay(_appConfig.PollingInterval, _cancellationTokenSource.Token)

            Catch ex As OperationCanceledException
                Exit While
            Catch ex As Exception
                ' تجاهل الأخطاء والمحاولة مرة أخرى
                Try
                    Task.Delay(2000, _cancellationTokenSource.Token).Wait()
                Catch
                    ' تجاهل أخطاء التأخير
                End Try
            End Try
        End While
    End Function

    ' معالجة رد المستخدم
    Private Async Function ProcessCallbackQueryAsync(callbackQuery As CallbackQuery) As Task
        Try
            If callbackQuery Is Nothing Then Return
            If String.IsNullOrEmpty(callbackQuery.callback_data) Then Return

            Dim parts() As String = callbackQuery.callback_data.Split({"_"c})
            If parts Is Nothing OrElse parts.Length <> 2 Then Return

            Dim action As String = parts(0)
            Dim sessionId As String = parts(1)

            ' التحقق من صحة رمز الجلسة
            If String.IsNullOrEmpty(sessionId) OrElse sessionId <> _sessionManager.CurrentSessionId Then
                Return ' رمز جلسة غير صحيح
            End If

            ' معالجة الإجراء
            If Not String.IsNullOrEmpty(action) Then
                Select Case action.ToLower()
                    Case "approve"
                        _sessionManager.SetVerificationResult(VerificationResult.Approved)
                        RaiseEvent VerificationProgress("تم قبول طلب التحقق!")

                    Case "reject"
                        _sessionManager.SetVerificationResult(VerificationResult.Rejected)
                        RaiseEvent VerificationProgress("تم رفض طلب التحقق")
                End Select
            End If

        Catch ex As Exception
            ' تجاهل الأخطاء في معالجة الرد
            Console.WriteLine($"خطأ في معالجة CallbackQuery: {ex.Message}")
        End Try
    End Function

    ' إيقاف عملية التحقق
    Public Sub StopVerification()
        _isPolling = False
        _cancellationTokenSource?.Cancel()
    End Sub

    ' التحقق من صحة الإعدادات
    Public Function ValidateSettings() As Boolean
        Dim validation = _appConfig.ValidateSettings()
        Return validation.IsValid
    End Function

    ' الحصول على رسالة خطأ الإعدادات
    Public Function GetSettingsErrorMessage() As String
        Dim validation = _appConfig.ValidateSettings()
        If validation.IsValid Then
            Return "إعدادات البوت صحيحة"
        End If
        Return validation.ErrorMessage
    End Function

    Public Sub Dispose()
        StopVerification()
        _telegramBot?.Dispose()
    End Sub
End Class

Imports System.Security.Cryptography
Imports System.Text
Imports System.Net.NetworkInformation

Public Class SessionManager
    Private Shared _instance As SessionManager
    Private _currentSessionId As String
    Private _sessionStartTime As DateTime
    Private _isVerified As Boolean = False
    Private _verificationResult As VerificationResult = VerificationResult.Pending

    ' Singleton Pattern
    Public Shared ReadOnly Property Instance As SessionManager
        Get
            If _instance Is Nothing Then
                _instance = New SessionManager()
            End If
            Return _instance
        End Get
    End Property

    Private Sub New()
        ' منع إنشاء كائنات متعددة
    End Sub

    ' إنشاء رمز جلسة فريد وآمن
    Public Function GenerateSessionId() As String
        _currentSessionId = GenerateSecureUUID()
        _sessionStartTime = DateTime.Now
        _isVerified = False
        _verificationResult = VerificationResult.Pending
        Return _currentSessionId
    End Function

    ' توليد UUID آمن
    Private Function GenerateSecureUUID() As String
        Using rng As New RNGCryptoServiceProvider()
            Dim bytes(15) As Byte
            rng.GetBytes(bytes)

            ' تعيين الإصدار (4) والمتغير
            bytes(6) = CByte((bytes(6) And &HF) Or &H40)
            bytes(8) = CByte((bytes(8) And &H3F) Or &H80)

            Return New Guid(bytes).ToString("N").ToUpper()
        End Using
    End Function

    ' الحصول على معلومات الجهاز
    Public Function GetDeviceInfo() As String
        Try
            Dim deviceInfo As New StringBuilder()

            ' اسم الكمبيوتر
            deviceInfo.AppendLine($"Computer: {Environment.MachineName}")

            ' اسم المستخدم
            deviceInfo.AppendLine($"User: {Environment.UserName}")

            ' نظام التشغيل
            deviceInfo.AppendLine($"OS: {Environment.OSVersion}")

            ' معلومات إضافية من Environment
            Try
                deviceInfo.AppendLine($"Domain: {Environment.UserDomainName}")
                deviceInfo.AppendLine($"Processors: {Environment.ProcessorCount}")
                deviceInfo.AppendLine($"64-bit OS: {Environment.Is64BitOperatingSystem}")
                deviceInfo.AppendLine($"64-bit Process: {Environment.Is64BitProcess}")
            Catch
                ' تجاهل الأخطاء
            End Try

            Return deviceInfo.ToString().Trim()
        Catch ex As Exception
            Return $"Device: {Environment.MachineName} | User: {Environment.UserName}"
        End Try
    End Function

    ' الحصول على عنوان IP
    Public Function GetIPAddress() As String
        Try
            ' محاولة الحصول على IP الخارجي باستخدام HttpClient
            Using client As New System.Net.Http.HttpClient()
                client.Timeout = TimeSpan.FromSeconds(5)
                Dim task = client.GetStringAsync("https://api.ipify.org")
                task.Wait(5000) ' انتظار 5 ثوان
                If task.IsCompleted Then
                    Return task.Result.Trim()
                End If
            End Using
        Catch
            ' في حالة فشل الحصول على IP الخارجي، استخدم IP المحلي
            Try
                Dim host = System.Net.Dns.GetHostEntry(System.Net.Dns.GetHostName())
                For Each ip In host.AddressList
                    If ip.AddressFamily = System.Net.Sockets.AddressFamily.InterNetwork Then
                        Return ip.ToString()
                    End If
                Next
            Catch
            End Try
            Return "Unknown"
        End Try
    End Function

    ' التحقق من صحة الجلسة
    Public Function IsSessionValid() As Boolean
        If String.IsNullOrEmpty(_currentSessionId) Then Return False
        If _sessionStartTime = DateTime.MinValue Then Return False

        ' التحقق من انتهاء المهلة الزمنية
        Dim timeoutMinutes As Integer = AppConfig.Instance.SessionTimeoutMinutes
        If DateTime.Now.Subtract(_sessionStartTime).TotalMinutes > timeoutMinutes Then
            Return False
        End If

        Return True
    End Function

    ' تعيين نتيجة التحقق
    Public Sub SetVerificationResult(result As VerificationResult)
        _verificationResult = result
        _isVerified = (result = VerificationResult.Approved)
    End Sub

    ' الحصول على نتيجة التحقق
    Public ReadOnly Property VerificationResult As VerificationResult
        Get
            Return _verificationResult
        End Get
    End Property

    ' التحقق من حالة التحقق
    Public ReadOnly Property IsVerified As Boolean
        Get
            Return _isVerified AndAlso IsSessionValid()
        End Get
    End Property

    ' الحصول على رمز الجلسة الحالي
    Public ReadOnly Property CurrentSessionId As String
        Get
            Return _currentSessionId
        End Get
    End Property

    ' الحصول على وقت بداية الجلسة
    Public ReadOnly Property SessionStartTime As DateTime
        Get
            Return _sessionStartTime
        End Get
    End Property

    ' إعادة تعيين الجلسة
    Public Sub ResetSession()
        _currentSessionId = String.Empty
        _sessionStartTime = DateTime.MinValue
        _isVerified = False
        _verificationResult = VerificationResult.Pending
    End Sub

    ' التحقق من الأمان - منع التلاعب
    Public Function ValidateSecurityToken(providedToken As String) As Boolean
        If String.IsNullOrEmpty(providedToken) OrElse String.IsNullOrEmpty(_currentSessionId) Then
            Return False
        End If

        ' التحقق من تطابق الرمز مع التوقيت
        Dim expectedToken As String = GenerateSecurityToken(_currentSessionId, _sessionStartTime)
        Return String.Equals(providedToken, expectedToken, StringComparison.Ordinal)
    End Function

    ' توليد رمز أمان للجلسة
    Private Function GenerateSecurityToken(sessionId As String, startTime As DateTime) As String
        Dim data As String = $"{sessionId}|{startTime:yyyyMMddHHmmss}|{Environment.MachineName}"
        Using sha256 As SHA256 = SHA256.Create()
            Dim hash As Byte() = sha256.ComputeHash(Encoding.UTF8.GetBytes(data))
            Return Convert.ToBase64String(hash)
        End Using
    End Function

    ' الحصول على رمز الأمان للجلسة الحالية
    Public Function GetCurrentSecurityToken() As String
        If String.IsNullOrEmpty(_currentSessionId) Then Return String.Empty
        Return GenerateSecurityToken(_currentSessionId, _sessionStartTime)
    End Function
End Class

' تعداد نتائج التحقق
Public Enum VerificationResult
    Pending = 0
    Approved = 1
    Rejected = 2
    Timeout = 3
End Enum

# Telegram Bot Configuration Example
# Copy this file to TelegramConfig.ini and replace with your actual values

# Bot Token from @BotFather - Login_Active_Bot
# Your actual token: 7689386758:AAFaDStCjXMkiyrct3IMm2e1-9SzOKY6P6Y
BOT_TOKEN=7689386758:AAFaDStCjXMkiyrct3IMm2e1-9SzOKY6P6Y

# Your Telegram Chat ID
# Example: 123456789 or -123456789
CHAT_ID=YOUR_CHAT_ID_HERE

# Verification timeout in seconds (10-300)
VERIFICATION_TIMEOUT=60

# Polling interval in milliseconds (500-10000)
POLLING_INTERVAL=1000

# Session timeout in minutes (1-60)
SESSION_TIMEOUT_MINUTES=5

# Enable logging (true/false)
ENABLE_LOGGING=true

# Log level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# Telegram Bot Configuration
# Generated automatically with your bot credentials

# Your Bot Token from @BotFather - Login_Active_Bot
BOT_TOKEN=7689386758:AAFaDStCjXMkiyrct3IMm2e1-9SzOKY6P6Y

# Your Telegram Chat ID (replace with your actual Chat ID after getting it)
# To get your Chat ID:
# 1. Send a message to your bot: https://t.me/Login_Active_Bot
# 2. Visit: https://api.telegram.org/bot7689386758:AAFaDStCjXMkiyrct3IMm2e1-9SzOKY6P6Y/getUpdates
# 3. Find your Chat ID in the response under "chat":{"id":YOUR_CHAT_ID
CHAT_ID=YOUR_CHAT_ID_HERE

# Verification timeout in seconds (10-300)
VERIFICATION_TIMEOUT=60

# Polling interval in milliseconds (500-10000)
POLLING_INTERVAL=1000

# Session timeout in minutes (1-60)
SESSION_TIMEOUT_MINUTES=5

# Enable logging (true/false)
ENABLE_LOGGING=true

# Log level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO
